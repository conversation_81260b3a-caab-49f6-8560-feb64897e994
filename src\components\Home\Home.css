/* Modern Hero Section */
.hero-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  overflow: hidden;
  margin-top: 80px;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('../../assets/saloon-bg.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  z-index: 1;
}



.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 2;
}

.hero-content {
  position: relative;
  z-index: 3;
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 4rem;
  align-items: center;
  width: 100%;
}

.hero-text {
  color: white;
  max-width: 600px;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-size: var(--font-size-sm);
  font-weight: 500;
  margin-bottom: 2rem;
  animation: fadeInUp 0.8s ease-out;
}

.hero-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

.hero-title-accent {
  color: rgba(47, 126, 180, 0.50);
}

.hero-subtitle {
  font-size: var(--font-size-lg);
  line-height: 1.6;
  opacity: 0.9;
  margin-bottom: 2.5rem;
  animation: fadeInUp 0.8s ease-out 0.4s both;
}

.hero-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  animation: fadeInUp 0.8s ease-out 0.6s both;
}

.hero-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: var(--font-size-base);
}

.hero-btn.primary {
  background: white;
  color: var(--color-primary-600);
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);
}

.hero-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(255, 255, 255, 0.3);
}

.hero-btn.secondary {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.hero-btn.secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.hero-stats {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  animation: fadeInRight 0.8s ease-out 0.8s both;
}

.stat-item {
  text-align: center;
  color: white;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-number {
  display: block;
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: var(--font-size-sm);
  opacity: 0.8;
  font-weight: 500;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Section Styles */
.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-badge {
  display: inline-block;
  background: linear-gradient(135deg, var(--color-primary-100), var(--color-accent-100));
  color: var(--color-primary-700);
  padding: 0.5rem 1.5rem;
  border-radius: 50px;
  font-size: var(--font-size-sm);
  font-weight: 600;
  margin-bottom: 1rem;
}

.section-title {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 700;
  color: var(--color-gray-900);
  margin-bottom: 1rem;
  line-height: 1.2;
}

.section-subtitle {
  font-size: var(--font-size-lg);
  color: var(--color-gray-600);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Features Section */
.features-section {
  padding: 6rem 2rem;
  background: var(--color-gray-50);
}

.features-container {
  max-width: 1400px;
  margin: 0 auto;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.feature-card {
  background: white;
  padding: 2.5rem 2rem;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid var(--color-gray-100);
}

.feature-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
}

.feature-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--color-primary-500), var(--color-accent-500));
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  color: white;
}

.feature-icon svg {
  width: 32px;
  height: 32px;
}

.feature-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--color-gray-900);
  margin-bottom: 1rem;
}

.feature-description {
  color: var(--color-gray-600);
  line-height: 1.6;
}

/* Services Section */
.services-section {
  padding: 6rem 2rem;
  background: white;
}

.services-container {
  max-width: 1400px;
  margin: 0 auto;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2rem;
}

.service-card {
  background: var(--color-gray-50);
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid var(--color-gray-100);
}

.service-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.service-image {
  height: 120px;
  background: linear-gradient(135deg, var(--color-primary-500), var(--color-accent-500));
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.service-image::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="service-pattern" width="10" height="10" patternUnits="userSpaceOnUse"><circle cx="5" cy="5" r="0.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23service-pattern)"/></svg>');
}

.service-icon {
  font-size: 3rem;
  position: relative;
  z-index: 1;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.service-content {
  padding: 2rem;
}

.service-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--color-gray-900);
  margin-bottom: 0.75rem;
}

.service-description {
  color: var(--color-gray-600);
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.service-price {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--color-primary-600);
}

/* Testimonials Section */
.testimonials-section {
  padding: 6rem 2rem;
  background: linear-gradient(135deg, var(--color-gray-50) 0%, var(--color-primary-50) 100%);
}

.testimonials-container {
  max-width: 1400px;
  margin: 0 auto;
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.testimonial-card {
  background: white;
  border-radius: 20px;
  padding: 2.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid var(--color-gray-100);
}

.testimonial-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
}

.testimonial-stars {
  font-size: var(--font-size-lg);
  margin-bottom: 1.5rem;
}

.testimonial-text {
  color: var(--color-gray-700);
  line-height: 1.7;
  font-size: var(--font-size-base);
  margin-bottom: 2rem;
  font-style: italic;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.author-avatar {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, var(--color-primary-500), var(--color-accent-500));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: var(--font-size-lg);
}

.author-name {
  font-weight: 600;
  color: var(--color-gray-900);
  margin-bottom: 0.25rem;
}

.author-title {
  font-size: var(--font-size-sm);
  color: var(--color-gray-500);
}

/* CTA Section */
.cta-section {
  padding: 6rem 2rem;
  background: linear-gradient(135deg, var(--color-primary-600) 0%, var(--color-accent-600) 100%);
  position: relative;
  overflow: hidden;
}

.cta-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="cta-pattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23cta-pattern)"/></svg>');
  opacity: 0.3;
}

.cta-container {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
  position: relative;
  z-index: 1;
}

.cta-content {
  color: white;
}

.cta-title {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 700;
  margin-bottom: 1.5rem;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.cta-subtitle {
  font-size: var(--font-size-lg);
  opacity: 0.9;
  margin-bottom: 2.5rem;
  line-height: 1.6;
}

.cta-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.cta-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: var(--font-size-base);
}

.cta-btn.primary {
  background: white;
  color: var(--color-primary-600);
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);
}

.cta-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(255, 255, 255, 0.3);
}

.cta-btn.secondary {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.cta-btn.secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }

  .hero-stats {
    flex-direction: row;
    justify-content: center;
    gap: 1.5rem;
  }

  .features-grid,
  .services-grid,
  .testimonials-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }
}

@media (max-width: 768px) {
  .hero-section {
    margin-top: 70px;
  }

  .hero-content {
    padding: 1.5rem;
    gap: 2rem;
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .hero-btn {
    width: 100%;
    max-width: 280px;
    justify-content: center;
  }

  .hero-stats {
    flex-direction: column;
    gap: 1rem;
  }

  .stat-item {
    padding: 1rem;
  }

  .stat-number {
    font-size: 2rem;
  }

  .features-section,
  .services-section,
  .testimonials-section,
  .cta-section {
    padding: 4rem 1.5rem;
  }

  .section-header {
    margin-bottom: 3rem;
  }

  .features-grid,
  .services-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .testimonials-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .cta-actions {
    flex-direction: column;
    align-items: center;
  }

  .cta-btn {
    width: 100%;
    max-width: 280px;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .hero-content {
    padding: 1rem;
  }

  .hero-badge {
    font-size: var(--font-size-xs);
    padding: 0.375rem 0.75rem;
  }

  .feature-card,
  .service-card,
  .testimonial-card {
    padding: 1.5rem;
  }

  .service-content {
    padding: 1.5rem;
  }

  .cta-container {
    padding: 0 1rem;
  }
}

.beauty-title {
  text-align: center;
  font-size: 2.5rem;
  color: var(--color-primary-700);
  margin-bottom: 3rem;
  font-weight: 600;
}

.content-wrapper {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: center;
}

.text-content {
  padding: 2rem;
}

.trending-title {
  font-size: 1.2rem;
  color: var(--color-gray-800);
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.look-title {
  font-size: 1.8rem;
  color: var(--color-primary-700);
  margin-bottom: 1.5rem;
  font-weight: 700;
}

.description {
  color: var(--color-gray-600);
  margin-bottom: 1rem;
  line-height: 1.6;
}

.link-text {
  color: var(--color-secondary-600);
  font-weight: 500;
  cursor: pointer;
  transition: color 0.3s ease;
}

.link-text:hover {
  color: var(--color-secondary-700);
}

.image-content {
  display: flex;
  justify-content: center;
}

.bride-image {
  width: 100%;
  max-width: 400px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.bride-img {
  width: 100%;
  height: 300px;
  object-fit: cover;
  display: block;
}



/* Responsive Design */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .content-wrapper {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .beauty-title {
    font-size: 2rem;
  }

  .bride-img {
    height: 250px;
  }
}